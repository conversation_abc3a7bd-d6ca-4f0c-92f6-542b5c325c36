<!--
 * @Description: 联系我们页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-02
 * @Layout: 联系方式
-->
<template>
  <view class="contact-page">
    <!-- 联系方式 -->
    <view class="contact-methods">
      <view class="method-item" @click="callPhone">
        <view class="method-icon">📞</view>
        <view class="method-content">
          <view class="method-title">客服电话</view>
          <view class="method-desc">************</view>
          <view class="method-time">工作时间：9:00-18:00</view>
        </view>
        <view class="method-arrow">›</view>
      </view>

      <view class="method-item" @click="sendEmail">
        <view class="method-icon">📧</view>
        <view class="method-content">
          <view class="method-title">邮箱联系</view>
          <view class="method-desc"><EMAIL></view>
          <view class="method-time">24小时内回复</view>
        </view>
        <view class="method-arrow">›</view>
      </view>

      <view class="method-item" @click="openWechat">
        <view class="method-icon">💬</view>
        <view class="method-content">
          <view class="method-title">微信客服</view>
          <view class="method-desc">uni_assistant_service</view>
          <view class="method-time">在线时间：9:00-22:00</view>
        </view>
        <view class="method-arrow">›</view>
      </view>

      <view class="method-item" @click="joinQQ">
        <view class="method-icon">👥</view>
        <view class="method-content">
          <view class="method-title">QQ群</view>
          <view class="method-desc">123456789</view>
          <view class="method-time">用户交流群</view>
        </view>
        <view class="method-arrow">›</view>
      </view>
    </view>


  </view>
</template>

<script>
export default {
  name: 'ContactIndex',
  data() {
    return {}
  },

  methods: {
    /**
     * 拨打电话
     */
    callPhone() {
      uni.makePhoneCall({
        phoneNumber: '************'
      })
    },

    /**
     * 发送邮件
     */
    sendEmail() {
      uni.setClipboardData({
        data: '<EMAIL>',
        success: () => {
          uni.showToast({
            title: '邮箱地址已复制',
            icon: 'success'
          })
        }
      })
    },

    /**
     * 打开微信
     */
    openWechat() {
      uni.setClipboardData({
        data: 'uni_assistant_service',
        success: () => {
          uni.showToast({
            title: '微信号已复制',
            icon: 'success'
          })
        }
      })
    },

    /**
     * 加入QQ群
     */
    joinQQ() {
      uni.setClipboardData({
        data: '123456789',
        success: () => {
          uni.showToast({
            title: 'QQ群号已复制',
            icon: 'success'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.contact-page {
  min-height: calc(100vh - 100rpx);
  background: #ffffff;
  display: flex;
  flex-direction: column;
}
/* 联系方式 */
.contact-methods {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.method-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.method-item:last-child {
  border-bottom: none;
}

.method-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  margin-right: 24rpx;
}

.method-content {
  flex: 1;
}

.method-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.method-desc {
  font-size: 28rpx;
  color: #007aff;
  margin-bottom: 4rpx;
}

.method-time {
  font-size: 24rpx;
  color: #666;
}

.method-arrow {
  font-size: 32rpx;
  color: #ccc;
}


</style>
